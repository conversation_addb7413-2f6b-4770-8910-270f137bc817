# Rental Property Management System

A full-stack application built with Go (backend) and Next.js with TypeScript and Tailwind CSS (frontend).

## Setup
1. Install dependencies:
   - Backend: `cd backend && go mod tidy`
   - Frontend: `cd frontend && npm install`
2. Start services: `docker-compose up`
3. Access:
   - Frontend: http://localhost:3000
   - Backend: http://localhost:8080
   - Database: localhost:5432 (user: user, password: password, db: rental_db)

## Development
- Backend: Add API routes in `backend/cmd/main.go`.
- Frontend: Add pages in `frontend/pages` and components in `frontend/components`.
