package db

import (
	"database/sql"
	"fmt"
	"os"
	"strconv"

	_ "github.com/lib/pq"
)

// DBConfig holds database configuration
type DBConfig struct {
	Host     string
	User     string
	Password string
	Name     string
	Port     int
}

// GetDBConfigFromEnv reads database configuration from environment variables
func GetDBConfigFromEnv() DBConfig {
	config := DBConfig{
		Host:     getEnvWithDefault("DB_HOST", "localhost"),
		User:     getEnvWithDefault("DB_USER", "user"),
		Password: getEnvWithDefault("DB_PASS", "password"),
		Name:     getEnvWithDefault("DB_NAME", "rental_db"),
		Port:     getEnvIntWithDefault("DB_PORT", 5432),
	}
	return config
}

// getEnvWithDefault gets environment variable with a default value
func getEnvWithDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvIntWithDefault gets environment variable as int with a default value
func getEnvIntWithDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// CheckAndCreateDB checks if the PostgreSQL database exists, and creates it if not
func CheckAndCreateDB(config DBConfig) error {
	// Check if database exists
	exists, err := databaseExists(config)
	if err != nil {
		return fmt.Errorf("failed to check if database exists: %w", err)
	}

	if !exists {
		fmt.Printf("Database '%s' does not exist. Creating...\n", config.Name)

		// Create the database
		if err := createDatabase(config); err != nil {
			return fmt.Errorf("failed to create database: %w", err)
		}

		fmt.Printf("Database '%s' created successfully\n", config.Name)
	} else {
		fmt.Printf("Database '%s' already exists\n", config.Name)
	}

	return nil
}

// databaseExists checks if the specified database exists
func databaseExists(config DBConfig) (bool, error) {
	// Connect to postgres database to check if target database exists
	postgresURL := fmt.Sprintf("postgres://%s:%s@%s:%d/postgres?sslmode=disable",
		config.User, config.Password, config.Host, config.Port)

	db, err := sql.Open("postgres", postgresURL)
	if err != nil {
		return false, fmt.Errorf("failed to connect to postgres: %w", err)
	}
	defer db.Close()

	// Test the connection
	if err := db.Ping(); err != nil {
		return false, fmt.Errorf("failed to ping postgres: %w", err)
	}

	// Check if database exists
	var exists bool
	query := "SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = $1)"
	err = db.QueryRow(query, config.Name).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check database existence: %w", err)
	}

	return exists, nil
}

// createDatabase creates a new PostgreSQL database
func createDatabase(config DBConfig) error {
	// Connect to postgres database to create the target database
	postgresURL := fmt.Sprintf("postgres://%s:%s@%s:%d/postgres?sslmode=disable",
		config.User, config.Password, config.Host, config.Port)

	db, err := sql.Open("postgres", postgresURL)
	if err != nil {
		return fmt.Errorf("failed to connect to postgres: %w", err)
	}
	defer db.Close()

	// Test the connection
	if err := db.Ping(); err != nil {
		return fmt.Errorf("failed to ping postgres: %w", err)
	}

	// Create the database
	query := fmt.Sprintf("CREATE DATABASE %s", config.Name)
	_, err = db.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to create database: %w", err)
	}

	return nil
}
