package api

import (
	"database/sql"
	"encoding/json"
	"net/http"

	"github.com/go-chi/chi"
	"github.com/nyunja/rental-property-system/backend/internal/api/handlers"
)

type Response struct {
	Message string `json:"message"`
	Status  string `json:"status"`
}

func SetupRouter(db *sql.DB) *chi.Mux {
	r := chi.NewRouter()
	handler := &handlers.PropertyHandler{DB: db}
	r.Get("/", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(200)
		json.NewEncoder(w).Encode(Response{
			Message: "Rental Property Management API",
			Status:  "Success",
		})
	})
	r.Get("/api/v1/properties", handler.ListProperties)
	return r
}
