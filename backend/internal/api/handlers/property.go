package handlers

import (
	"database/sql"
	"encoding/json"
	"net/http"

	"github.com/nyunja/rental-property-system/backend/internal/models"
)

type PropertyHandler struct {
	DB *sql.DB
}

func (h *PropertyHandler) ListProperties(w http.ResponseWriter, r *http.Request) {
	rows, err := h.DB.Query(`
        SELECT property_id, owner_id, address, city, state, zip_code, 
               property_type, bedrooms, bathrooms, rent_amount, 
               created_at, updated_at 
        FROM properties
    `)
	if err != nil {
		http.Error(w, "Failed to fetch properties", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var properties []models.Property
	for rows.Next() {
		var p models.Property
		err := rows.Scan(
			&p.PropertyID, &p.OwnerID, &p.Address, &p.City, &p.State, &p.ZipCode,
			&p.PropertyType, &p.Bedrooms, &p.Bathrooms, &p.RentAmount,
			&p.CreatedAt, &p.UpdatedAt,
		)
		if err != nil {
			http.Error(w, "Failed to scan properties", http.StatusInternalServerError)
			return
		}
		properties = append(properties, p)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(properties)
}
