package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PropertyType represents the type of property
type PropertyType string

const (
	PropertyTypeApartment PropertyType = "apartment"
	PropertyTypeHouse     PropertyType = "house"
	PropertyTypeCondo     PropertyType = "condo"
	PropertyTypeTownhouse PropertyType = "townhouse"
	PropertyTypeStudio    PropertyType = "studio"
	PropertyTypeDuplex    PropertyType = "duplex"
)

// Property represents a rental property in the system
type Property struct {
	PropertyID   uuid.UUID    `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"property_id"`
	OwnerID      uuid.UUID    `gorm:"type:uuid;not null;index" json:"owner_id"`
	Address      string       `gorm:"type:varchar(255);not null" json:"address"`
	City         string       `gorm:"type:varchar(100)" json:"city"`
	State        string       `gorm:"type:varchar(50)" json:"state"`
	ZipCode      string       `gorm:"type:varchar(20)" json:"zip_code"`
	PropertyType PropertyType `gorm:"type:varchar(50)" json:"property_type"`
	Bedrooms     int          `gorm:"type:integer" json:"bedrooms"`
	Bathrooms    int          `gorm:"type:integer" json:"bathrooms"`
	RentAmount   float64      `gorm:"type:decimal(10,2);not null" json:"rent_amount"`
	CreatedAt    time.Time    `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at"`

	// Relationships
	Owner               User                 `gorm:"foreignKey:OwnerID" json:"owner,omitempty"`
	Leases              []Lease              `gorm:"foreignKey:PropertyID" json:"leases,omitempty"`
	MaintenanceRequests []MaintenanceRequest `gorm:"foreignKey:PropertyID" json:"maintenance_requests,omitempty"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (p *Property) BeforeCreate(tx *gorm.DB) error {
	if p.PropertyID == uuid.Nil {
		p.PropertyID = uuid.New()
	}
	return nil
}

// TableName specifies the table name for the Property model
func (Property) TableName() string {
	return "properties"
}

// GetFullAddress returns the complete address of the property
func (p *Property) GetFullAddress() string {
	address := p.Address
	if p.City != "" {
		address += ", " + p.City
	}
	if p.State != "" {
		address += ", " + p.State
	}
	if p.ZipCode != "" {
		address += " " + p.ZipCode
	}
	return address
}

// GetActiveLeases returns all active leases for this property
func (p *Property) GetActiveLeases() []Lease {
	var activeLeases []Lease
	for _, lease := range p.Leases {
		if lease.Status == LeaseStatusActive {
			activeLeases = append(activeLeases, lease)
		}
	}
	return activeLeases
}

// HasActiveLease checks if the property has any active leases
func (p *Property) HasActiveLease() bool {
	return len(p.GetActiveLeases()) > 0
}
