package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MaintenanceStatus represents the status of a maintenance request
type MaintenanceStatus string

const (
	MaintenanceStatusOpen       MaintenanceStatus = "open"
	MaintenanceStatusInProgress MaintenanceStatus = "in_progress"
	MaintenanceStatusResolved   MaintenanceStatus = "resolved"
	MaintenanceStatusClosed     MaintenanceStatus = "closed"
)

// MaintenancePriority represents the priority level of a maintenance request
type MaintenancePriority string

const (
	MaintenancePriorityLow    MaintenancePriority = "low"
	MaintenancePriorityMedium MaintenancePriority = "medium"
	MaintenancePriorityHigh   MaintenancePriority = "high"
	MaintenancePriorityUrgent MaintenancePriority = "urgent"
)

// MaintenanceRequest represents maintenance issues reported by tenants for properties
type MaintenanceRequest struct {
	RequestID   uuid.UUID           `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"request_id"`
	PropertyID  uuid.UUID           `gorm:"type:uuid;not null;index" json:"property_id"`
	TenantID    uuid.UUID           `gorm:"type:uuid;not null;index" json:"tenant_id"`
	Description string              `gorm:"type:text;not null" json:"description"`
	Status      MaintenanceStatus   `gorm:"type:varchar(20);default:'open'" json:"status"`
	Priority    MaintenancePriority `gorm:"type:varchar(20);default:'medium'" json:"priority"`
	CreatedAt   time.Time           `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`

	// Relationships
	Property Property `gorm:"foreignKey:PropertyID" json:"property,omitempty"`
	Tenant   Tenant   `gorm:"foreignKey:TenantID" json:"tenant,omitempty"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (mr *MaintenanceRequest) BeforeCreate(tx *gorm.DB) error {
	if mr.RequestID == uuid.Nil {
		mr.RequestID = uuid.New()
	}
	return nil
}

// TableName specifies the table name for the MaintenanceRequest model
func (MaintenanceRequest) TableName() string {
	return "maintenance_requests"
}

// IsOpen checks if the maintenance request is open
func (mr *MaintenanceRequest) IsOpen() bool {
	return mr.Status == MaintenanceStatusOpen
}

// IsInProgress checks if the maintenance request is in progress
func (mr *MaintenanceRequest) IsInProgress() bool {
	return mr.Status == MaintenanceStatusInProgress
}

// IsResolved checks if the maintenance request is resolved
func (mr *MaintenanceRequest) IsResolved() bool {
	return mr.Status == MaintenanceStatusResolved
}

// IsClosed checks if the maintenance request is closed
func (mr *MaintenanceRequest) IsClosed() bool {
	return mr.Status == MaintenanceStatusClosed
}

// IsHighPriority checks if the maintenance request is high priority
func (mr *MaintenanceRequest) IsHighPriority() bool {
	return mr.Priority == MaintenancePriorityHigh || mr.Priority == MaintenancePriorityUrgent
}

// IsUrgent checks if the maintenance request is urgent
func (mr *MaintenanceRequest) IsUrgent() bool {
	return mr.Priority == MaintenancePriorityUrgent
}

// GetAge calculates how many days old the maintenance request is
func (mr *MaintenanceRequest) GetAge() int {
	return int(time.Now().Sub(mr.CreatedAt).Hours() / 24)
}

// GetStatusColor returns a color code based on the status for UI purposes
func (mr *MaintenanceRequest) GetStatusColor() string {
	switch mr.Status {
	case MaintenanceStatusOpen:
		return "#ff6b6b" // Red
	case MaintenanceStatusInProgress:
		return "#ffd93d" // Yellow
	case MaintenanceStatusResolved:
		return "#6bcf7f" // Green
	case MaintenanceStatusClosed:
		return "#95a5a6" // Gray
	default:
		return "#95a5a6" // Gray
	}
}

// GetPriorityColor returns a color code based on the priority for UI purposes
func (mr *MaintenanceRequest) GetPriorityColor() string {
	switch mr.Priority {
	case MaintenancePriorityLow:
		return "#6bcf7f" // Green
	case MaintenancePriorityMedium:
		return "#ffd93d" // Yellow
	case MaintenancePriorityHigh:
		return "#ff9f43" // Orange
	case MaintenancePriorityUrgent:
		return "#ff6b6b" // Red
	default:
		return "#95a5a6" // Gray
	}
}
