package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Tenant represents tenant-specific information linked to a user account
type Tenant struct {
	TenantID               uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"tenant_id"`
	UserID                 uuid.UUID `gorm:"type:uuid;not null;uniqueIndex" json:"user_id"`
	EmergencyContactName   string    `gorm:"type:varchar(255)" json:"emergency_contact_name"`
	EmergencyContactPhone  string    `gorm:"type:varchar(20)" json:"emergency_contact_phone"`
	CreatedAt              time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt              time.Time `json:"updated_at"`

	// Relationships
	User                User                 `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Leases              []Lease              `gorm:"foreignKey:TenantID" json:"leases,omitempty"`
	Payments            []Payment            `gorm:"foreignKey:TenantID" json:"payments,omitempty"`
	MaintenanceRequests []MaintenanceRequest `gorm:"foreignKey:TenantID" json:"maintenance_requests,omitempty"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (t *Tenant) BeforeCreate(tx *gorm.DB) error {
	if t.TenantID == uuid.Nil {
		t.TenantID = uuid.New()
	}
	return nil
}

// TableName specifies the table name for the Tenant model
func (Tenant) TableName() string {
	return "tenants"
}

// GetActiveLeases returns all active leases for this tenant
func (t *Tenant) GetActiveLeases() []Lease {
	var activeLeases []Lease
	for _, lease := range t.Leases {
		if lease.Status == LeaseStatusActive {
			activeLeases = append(activeLeases, lease)
		}
	}
	return activeLeases
}

// HasActiveLease checks if the tenant has any active leases
func (t *Tenant) HasActiveLease() bool {
	return len(t.GetActiveLeases()) > 0
}

// GetFullName returns the tenant's full name from the associated user
func (t *Tenant) GetFullName() string {
	if t.User.UserID != uuid.Nil {
		return t.User.GetFullName()
	}
	return ""
}

// GetEmail returns the tenant's email from the associated user
func (t *Tenant) GetEmail() string {
	if t.User.UserID != uuid.Nil {
		return t.User.Email
	}
	return ""
}

// GetPhone returns the tenant's phone from the associated user
func (t *Tenant) GetPhone() string {
	if t.User.UserID != uuid.Nil {
		return t.User.Phone
	}
	return ""
}
