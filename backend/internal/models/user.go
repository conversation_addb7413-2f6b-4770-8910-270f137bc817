package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserRole represents the role of a user in the system
type UserRole string

const (
	RoleLandlord UserRole = "landlord"
	RoleTenant   UserRole = "tenant"
	RoleAdmin    UserRole = "admin"
)

// User represents a user in the rental property system
type User struct {
	UserID       uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"user_id"`
	Email        string    `gorm:"type:varchar(255);uniqueIndex;not null" json:"email"`
	PasswordHash string    `gorm:"type:varchar(255);not null" json:"-"` // Don't include in JSON responses
	Role         UserRole  `gorm:"type:varchar(20);not null" json:"role"`
	FirstName    string    `gorm:"type:varchar(100)" json:"first_name"`
	LastName     string    `gorm:"type:varchar(100)" json:"last_name"`
	Phone        string    `gorm:"type:varchar(20)" json:"phone"`
	CreatedAt    time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// Relationships
	Properties          []Property           `gorm:"foreignKey:OwnerID" json:"properties,omitempty"`
	Tenant              *Tenant              `gorm:"foreignKey:UserID" json:"tenant,omitempty"`
	MaintenanceRequests []MaintenanceRequest `gorm:"foreignKey:TenantID;references:UserID" json:"maintenance_requests,omitempty"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.UserID == uuid.Nil {
		u.UserID = uuid.New()
	}
	return nil
}

// TableName specifies the table name for the User model
func (User) TableName() string {
	return "users"
}

// IsLandlord checks if the user is a landlord
func (u *User) IsLandlord() bool {
	return u.Role == RoleLandlord
}

// IsTenant checks if the user is a tenant
func (u *User) IsTenant() bool {
	return u.Role == RoleTenant
}

// IsAdmin checks if the user is an admin
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	if u.FirstName != "" && u.LastName != "" {
		return u.FirstName + " " + u.LastName
	}
	if u.FirstName != "" {
		return u.FirstName
	}
	if u.LastName != "" {
		return u.LastName
	}
	return u.Email
}
