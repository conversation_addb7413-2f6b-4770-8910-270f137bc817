package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PaymentType represents the type of payment
type PaymentType string

const (
	PaymentTypeRent     PaymentType = "rent"
	PaymentTypeDeposit  PaymentType = "deposit"
	PaymentTypeLateFee  PaymentType = "late_fee"
	PaymentTypeUtility  PaymentType = "utility"
	PaymentTypePenalty  PaymentType = "penalty"
	PaymentTypeRefund   PaymentType = "refund"
)

// PaymentStatus represents the status of a payment
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusCompleted PaymentStatus = "completed"
	PaymentStatusFailed    PaymentStatus = "failed"
	PaymentStatusCancelled PaymentStatus = "cancelled"
)

// Payment represents rent payments and other fees
type Payment struct {
	PaymentID   uuid.UUID     `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"payment_id"`
	LeaseID     uuid.UUID     `gorm:"type:uuid;not null;index" json:"lease_id"`
	TenantID    uuid.UUID     `gorm:"type:uuid;not null;index" json:"tenant_id"`
	Amount      float64       `gorm:"type:decimal(10,2);not null" json:"amount"`
	PaymentDate time.Time     `gorm:"type:date;not null" json:"payment_date"`
	PaymentType PaymentType   `gorm:"type:varchar(20);not null" json:"payment_type"`
	Status      PaymentStatus `gorm:"type:varchar(20);default:'pending'" json:"status"`
	CreatedAt   time.Time     `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`

	// Relationships
	Lease  Lease  `gorm:"foreignKey:LeaseID" json:"lease,omitempty"`
	Tenant Tenant `gorm:"foreignKey:TenantID" json:"tenant,omitempty"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (p *Payment) BeforeCreate(tx *gorm.DB) error {
	if p.PaymentID == uuid.Nil {
		p.PaymentID = uuid.New()
	}
	return nil
}

// TableName specifies the table name for the Payment model
func (Payment) TableName() string {
	return "payments"
}

// IsPending checks if the payment is pending
func (p *Payment) IsPending() bool {
	return p.Status == PaymentStatusPending
}

// IsCompleted checks if the payment is completed
func (p *Payment) IsCompleted() bool {
	return p.Status == PaymentStatusCompleted
}

// IsFailed checks if the payment has failed
func (p *Payment) IsFailed() bool {
	return p.Status == PaymentStatusFailed
}

// IsCancelled checks if the payment has been cancelled
func (p *Payment) IsCancelled() bool {
	return p.Status == PaymentStatusCancelled
}

// IsRentPayment checks if this is a rent payment
func (p *Payment) IsRentPayment() bool {
	return p.PaymentType == PaymentTypeRent
}

// IsDepositPayment checks if this is a deposit payment
func (p *Payment) IsDepositPayment() bool {
	return p.PaymentType == PaymentTypeDeposit
}

// IsLateFee checks if this is a late fee payment
func (p *Payment) IsLateFee() bool {
	return p.PaymentType == PaymentTypeLateFee
}

// IsOverdue checks if the payment is overdue (past due date and not completed)
func (p *Payment) IsOverdue() bool {
	return time.Now().After(p.PaymentDate) && !p.IsCompleted()
}

// GetDaysOverdue calculates how many days the payment is overdue
func (p *Payment) GetDaysOverdue() int {
	if !p.IsOverdue() {
		return 0
	}
	return int(time.Now().Sub(p.PaymentDate).Hours() / 24)
}
