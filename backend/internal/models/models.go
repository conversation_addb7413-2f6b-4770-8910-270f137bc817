package models

import (
	"gorm.io/gorm"
)

// AllModels returns a slice of all model structs for migration purposes
func AllModels() []interface{} {
	return []interface{}{
		&User{},
		&Property{},
		&Tenant{},
		&Lease{},
		&Payment{},
		&MaintenanceRequest{},
	}
}

// AutoMigrate runs auto migration for all models
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(AllModels()...)
}

// CreateIndexes creates additional indexes that GORM doesn't handle automatically
func CreateIndexes(db *gorm.DB) error {
	// Create composite index for lease overlapping prevention
	// This helps enforce the constraint that no property can have overlapping active leases
	if err := db.Exec(`
		CREATE UNIQUE INDEX IF NOT EXISTS idx_property_active_lease 
		ON leases (property_id) 
		WHERE status = 'active'
	`).Error; err != nil {
		return err
	}

	// Create index for payment queries by date range
	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_payments_date_status 
		ON payments (payment_date, status)
	`).Error; err != nil {
		return err
	}

	// Create index for maintenance requests by status and priority
	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_maintenance_status_priority 
		ON maintenance_requests (status, priority)
	`).Error; err != nil {
		return err
	}

	// Create index for lease date ranges
	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_lease_dates 
		ON leases (start_date, end_date)
	`).Error; err != nil {
		return err
	}

	return nil
}

// DropIndexes drops the custom indexes (useful for testing or rollbacks)
func DropIndexes(db *gorm.DB) error {
	indexes := []string{
		"idx_property_active_lease",
		"idx_payments_date_status",
		"idx_maintenance_status_priority",
		"idx_lease_dates",
	}

	for _, index := range indexes {
		if err := db.Exec("DROP INDEX IF EXISTS " + index).Error; err != nil {
			return err
		}
	}

	return nil
}

// SeedData creates initial data for development/testing
func SeedData(db *gorm.DB) error {
	// This function can be used to seed initial data
	// For now, it's empty but can be extended as needed
	return nil
}
