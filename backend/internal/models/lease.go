package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// LeaseStatus represents the status of a lease
type LeaseStatus string

const (
	LeaseStatusActive     LeaseStatus = "active"
	LeaseStatusExpired    LeaseStatus = "expired"
	LeaseStatusTerminated LeaseStatus = "terminated"
)

// Lease represents a lease agreement between tenants and properties
type Lease struct {
	LeaseID       uuid.UUID   `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"lease_id"`
	PropertyID    uuid.UUID   `gorm:"type:uuid;not null;index" json:"property_id"`
	TenantID      uuid.UUID   `gorm:"type:uuid;not null;index" json:"tenant_id"`
	StartDate     time.Time   `gorm:"type:date;not null" json:"start_date"`
	EndDate       time.Time   `gorm:"type:date;not null" json:"end_date"`
	MonthlyRent   float64     `gorm:"type:decimal(10,2);not null" json:"monthly_rent"`
	DepositAmount float64     `gorm:"type:decimal(10,2)" json:"deposit_amount"`
	Status        LeaseStatus `gorm:"type:varchar(20);default:'active'" json:"status"`
	CreatedAt     time.Time   `gorm:"not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt     time.Time   `json:"updated_at"`

	// Relationships
	Property Property  `gorm:"foreignKey:PropertyID" json:"property,omitempty"`
	Tenant   Tenant    `gorm:"foreignKey:TenantID" json:"tenant,omitempty"`
	Payments []Payment `gorm:"foreignKey:LeaseID" json:"payments,omitempty"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (l *Lease) BeforeCreate(tx *gorm.DB) error {
	if l.LeaseID == uuid.Nil {
		l.LeaseID = uuid.New()
	}
	return nil
}

// TableName specifies the table name for the Lease model
func (Lease) TableName() string {
	return "leases"
}

// IsActive checks if the lease is currently active
func (l *Lease) IsActive() bool {
	return l.Status == LeaseStatusActive
}

// IsExpired checks if the lease has expired
func (l *Lease) IsExpired() bool {
	return l.Status == LeaseStatusExpired || time.Now().After(l.EndDate)
}

// IsTerminated checks if the lease has been terminated
func (l *Lease) IsTerminated() bool {
	return l.Status == LeaseStatusTerminated
}

// GetDurationInMonths calculates the lease duration in months
func (l *Lease) GetDurationInMonths() int {
	years := l.EndDate.Year() - l.StartDate.Year()
	months := int(l.EndDate.Month()) - int(l.StartDate.Month())
	return years*12 + months
}

// GetTotalRentAmount calculates the total rent amount for the lease duration
func (l *Lease) GetTotalRentAmount() float64 {
	return l.MonthlyRent * float64(l.GetDurationInMonths())
}

// GetRemainingDays calculates the remaining days in the lease
func (l *Lease) GetRemainingDays() int {
	if time.Now().After(l.EndDate) {
		return 0
	}
	return int(l.EndDate.Sub(time.Now()).Hours() / 24)
}

// GetTotalPaid calculates the total amount paid for this lease
func (l *Lease) GetTotalPaid() float64 {
	var total float64
	for _, payment := range l.Payments {
		if payment.Status == PaymentStatusCompleted {
			total += payment.Amount
		}
	}
	return total
}

// GetOutstandingBalance calculates the outstanding balance for this lease
func (l *Lease) GetOutstandingBalance() float64 {
	totalRent := l.GetTotalRentAmount()
	totalPaid := l.GetTotalPaid()
	return totalRent - totalPaid
}
