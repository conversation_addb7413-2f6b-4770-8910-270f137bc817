package config

import (
	"os"
	"strconv"
)

// DBConfig holds database configuration
type DBConfig struct {
	Host     string
	User     string
	Password string
	Name     string
	Port     int
}

// GetDBConfigFromEnv reads database configuration from environment variables
func GetDBConfigFromEnv() DBConfig {
	config := DBConfig{
		Host:     getEnvWithDefault("DB_HOST", "localhost"),
		User:     getEnvWithDefault("DB_USER", "user"),
		Password: getEnvWithDefault("DB_PASS", "password"),
		Name:     getEnvWithDefault("DB_NAME", "rental_db"),
		Port:     getEnvIntWithDefault("DB_PORT", 5432),
	}
	return config
}

// getEnvWithDefault gets environment variable with a default value
func getEnvWithDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvIntWithDefault gets environment variable as int with a default value
func getEnvIntWithDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
