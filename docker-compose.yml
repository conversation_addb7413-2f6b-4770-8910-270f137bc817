version: '3'
services:
  backend:
    build: ./backend
    ports:
      - "8080:8080"
    depends_on:
      - db
    env_file:
      - .env
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    env_file:
      - .env
    volumes:
      - ./frontend:/app
      - /app/node_modules
  db:
    image: postgres:13
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: rental_db
    ports:
      - "5433:5432"
