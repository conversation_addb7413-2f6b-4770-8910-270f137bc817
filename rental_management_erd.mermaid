erDiagram
    Users ||--|| Tenants : "1:1"
    Users ||--o{ Properties : "1:N (landlord owns)"
    Properties ||--o{ Leases : "1:N"
    Tenants ||--o{ Leases : "1:N"
    Leases ||--o{ Payments : "1:N"
    Properties ||--o{ Maintenance_Requests : "1:N"
    Tenants ||--o{ Maintenance_Requests : "1:N"

    Users {
        UUID user_id PK
        VARCHAR email UK "UNIQUE, NOT NULL"
        VARCHAR password_hash "NOT NULL"
        ENUM role "landlord, tenant, admin - NOT NULL"
        VARCHAR first_name
        VARCHAR last_name
        VARCHAR phone
        TIMESTAMP created_at "NOT NULL, default CURRENT_TIMESTAMP"
        TIMESTAMP updated_at
    }

    Tenants {
        UUID tenant_id PK
        UUID user_id FK "UNIQUE -> Users.user_id, NOT NULL"
        VARCHAR emergency_contact_name
        VARCHAR emergency_contact_phone
        TIMESTAMP created_at "NOT NULL"
        TIMESTAMP updated_at
    }

    Properties {
        UUID property_id PK
        UUID owner_id FK "-> Users.user_id, NOT NULL"
        VARCHAR address "NOT NULL"
        VARCHAR city
        VARCHAR state
        VARCHAR zip_code
        ENUM property_type "apartment, house, condo, etc."
        INTEGER bedrooms
        INTEGER bathrooms
        DECIMAL rent_amount "NOT NULL"
        TIMESTAMP created_at "NOT NULL"
        TIMESTAMP updated_at
    }

    Leases {
        UUID lease_id PK
        UUID property_id FK "-> Properties.property_id, NOT NULL"
        UUID tenant_id FK "-> Tenants.tenant_id, NOT NULL"
        DATE start_date "NOT NULL"
        DATE end_date "NOT NULL"
        DECIMAL monthly_rent "NOT NULL"
        DECIMAL deposit_amount
        ENUM status "active, expired, terminated"
        TIMESTAMP created_at "NOT NULL"
        TIMESTAMP updated_at
    }

    Payments {
        UUID payment_id PK
        UUID lease_id FK "-> Leases.lease_id, NOT NULL"
        UUID tenant_id FK "-> Tenants.tenant_id, NOT NULL"
        DECIMAL amount "NOT NULL"
        DATE payment_date "NOT NULL"
        ENUM payment_type "rent, deposit, late_fee, etc."
        ENUM status "pending, completed, failed"
        TIMESTAMP created_at "NOT NULL"
        TIMESTAMP updated_at
    }

    Maintenance_Requests {
        UUID request_id PK
        UUID property_id FK "-> Properties.property_id, NOT NULL"
        UUID tenant_id FK "-> Tenants.tenant_id, NOT NULL"
        TEXT description "NOT NULL"
        ENUM status "open, in_progress, resolved, closed"
        ENUM priority "low, medium, high"
        TIMESTAMP created_at "NOT NULL"
        TIMESTAMP updated_at
    }