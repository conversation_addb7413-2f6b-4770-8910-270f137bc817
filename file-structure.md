rental-property-system/
├── backend/                      # Go backend
│   ├── cmd/                     # Application entry points
│   │   └── main.go              # Main server startup
│   ├── internal/                # Private packages (business logic)
│   │   ├── config/              # Config loading (e.g., env vars)
│   │   │   └── config.go
│   │   ├── database/            # DB connections and migrations
│   │   │   ├── migrations/      # SQL migration files (e.g., 001_create_users.up.sql)
│   │   │   └── db.go
│   │   ├── handlers/            # API handlers
│   │   │   ├── properties.go    # CRUD for properties
│   │   │   ├── users.go         # Auth and user management
│   │   │   └── ...              # Handlers for leases, payments, etc.
│   │   ├── middleware/          # Auth, logging, etc.
│   │   │   └── auth.go
│   │   ├── models/              # GORM structs matching ERD
│   │   │   ├── user.go
│   │   │   ├── property.go
│   │   │   └── ...              # Models for tenants, leases, etc.
│   │   └── services/            # Business logic services
│   │       ├── property_service.go
│   │       └── ...              # Services for payments, maintenance
│   ├── pkg/                     # Shared utilities (e.g., validators)
│   │   └── utils.go
│   ├── tests/                   # Unit/integration tests
│   │   └── handlers_test.go
│   ├── go.mod                   # Go dependencies
│   ├── go.sum
│   ├── .env                     # Environment variables (git ignored)
│   └── Dockerfile               # For building backend image
├── frontend/                    # Next.js frontend
│   ├── components/              # Reusable UI components
│   │   ├── auth/                # Login/Register forms
│   │   │   └── LoginForm.tsx
│   │   ├── dashboard/           # Role-based dashboards
│   │   │   └── LandlordDashboard.tsx
│   │   ├── forms/               # Forms for properties, payments, etc.
│   │   │   └── PropertyForm.tsx
│   │   ├── layout/              # Shared layouts (e.g., navbar)
│   │   │   └── Navbar.tsx
│   │   └── ui/                  # Generic UI (buttons, modals)
│   │       └── Button.tsx
│   ├── pages/                   # Next.js pages/routes
│   │   ├── api/                 # API routes (if needed for proxying)
│   │   │   └── auth.ts          # Optional auth API
│   │   ├── dashboard.tsx        # Main dashboard
│   │   ├── index.tsx            # Homepage/login redirect
│   │   ├── properties/          # Property-related pages
│   │   │   ├── [id].tsx         # Dynamic property detail
│   │   │   └── index.tsx        # List properties
│   │   ├── leases.tsx           # Leases page
│   │   ├── payments.tsx         # Payments page
│   │   ├── maintenance.tsx      # Maintenance requests
│   │   └── _app.tsx             # App wrapper (for providers)
│   ├── public/                  # Static assets
│   │   ├── images/              # Logos, placeholders
│   │   └── favicon.ico
│   ├── styles/                  # CSS files
│   │   └── globals.css          # Tailwind base
│   ├── types/                   # TypeScript interfaces
│   │   ├── index.ts             # Exports all types
│   │   ├── property.ts          # Interface Property {}
│   │   └── ...                  # Types for user, lease, etc.
│   ├── utils/                   # Helpers (e.g., API client)
│   │   └── api.ts               # Axios instance
│   ├── tests/                   # Frontend tests
│   │   └── components/          # Jest tests for components
│   ├── next.config.js           # Next.js config
│   ├── tsconfig.json            # TypeScript config
│   ├── tailwind.config.js       # Tailwind config
│   ├── package.json             # NPM dependencies
│   ├── .env.local               # Frontend env vars (git ignored)
│   └── Dockerfile               # For building frontend image
├── docker-compose.yml           # Local services (DB, backend, frontend)
├── .gitignore                   # Ignore node_modules, .env, etc.
├── README.md                    # Project docs and setup guide
└── .github/workflows/           # CI/CD pipelines (e.g., ci.yml for tests)
    └── deploy.yml