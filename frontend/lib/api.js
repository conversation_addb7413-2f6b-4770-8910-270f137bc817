const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

const apiCall = async (endpoint, options = {}) => {
  const response = await fetch(`${API_BASE}${endpoint}`, {
    credentials: "include",
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`API Error: ${response.status}`);
  }

  return response.json();
};

const homeAPI = {
    home: async () => apiCall('/')
}

export { homeAPI };
