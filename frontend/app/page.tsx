import { homeAPI } from "../lib/api";

export default async function Home() {
  const { home } = homeAPI;
  const data = await home();
  console.log(data);
  return (
    <div className="font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20">
      <h1 className="text-3xl font-bold">Home</h1>
      <p>{data.message}</p>
      <p className="text-sm text-gray-600">Status: {data.status}</p>
    </div>
  );
}
